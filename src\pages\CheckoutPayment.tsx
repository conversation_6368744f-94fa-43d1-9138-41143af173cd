import React, { useState, useEffect } from 'react';
import { CreditCard, Smartphone, ArrowLeft, Lock, MapPin, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Link, useNavigate } from 'react-router-dom';
import Header from '@/components/Header';
import CurrencyFormatter from '@/components/ui/currency-formatter';
import { useCart } from '@/contexts/CartContext';

// Kenyan counties for delivery
const KENYAN_COUNTIES = [
  'Nairobi', 'Mombasa', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Eldor<PERSON>', '<PERSON><PERSON><PERSON>',
  'Malindi', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>ga', 'Other'
];

const CheckoutPayment = () => {
  const { state } = useCart();
  const navigate = useNavigate();
  const [paymentMethod, setPaymentMethod] = useState('mpesa');
  const [isLoading, setIsLoading] = useState(false);
  const [deliveryInfo, setDeliveryInfo] = useState({
    address: '',
    county: '',
    phone: ''
  });
  const [checkoutSummary, setCheckoutSummary] = useState(null);
  const [deliveryCost, setDeliveryCost] = useState(0);

  // Fetch checkout summary when county changes
  useEffect(() => {
    if (deliveryInfo.county) {
      fetchCheckoutSummary();
    }
  }, [deliveryInfo.county]);

  const fetchCheckoutSummary = async () => {
    try {
      // For guest checkout, we'll calculate delivery cost locally
      // since the backend checkout summary requires authentication
      if (deliveryInfo.county) {
        // Simple delivery cost calculation based on county
        const deliveryCosts: { [key: string]: number } = {
          'nairobi': 500,
          'mombasa': 800,
          'kisumu': 700,
          'nakuru': 600,
          'eldoret': 750,
          'thika': 550,
          'malindi': 900,
          'kitale': 800,
          'garissa': 1000,
          'kakamega': 700,
          'other': 1000
        };

        const cost = deliveryCosts[deliveryInfo.county.toLowerCase()] || 1000;
        setDeliveryCost(cost);

        // Create a simple checkout summary
        setCheckoutSummary({
          cart_items: state.items,
          subtotal_ksh: state.total,
          delivery_cost_ksh: cost,
          total_ksh: state.total + cost
        });
      }
    } catch (error) {
      console.error('Error calculating checkout summary:', error);
    }
  };

  const handleDeliveryInfoChange = (field: string, value: string) => {
    setDeliveryInfo(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!deliveryInfo.address || !deliveryInfo.county) {
      alert('Please fill in all delivery information');
      return;
    }

    if (!deliveryInfo.phone) {
      alert('Please provide a phone number for payment');
      return;
    }

    if (state.items.length === 0) {
      alert('Your cart is empty');
      return;
    }

    setIsLoading(true);

    try {
      // Prepare cart items for guest checkout
      const cartItems = state.items.map(item => ({
        product_id: parseInt(item.id),
        quantity: item.quantity
      }));

      const paymentData = {
        delivery_address: deliveryInfo.address,
        delivery_county: deliveryInfo.county,
        payment_method: paymentMethod,
        phone: deliveryInfo.phone,
        // Guest customer information
        first_name: 'Guest',
        last_name: 'Customer',
        email: '<EMAIL>',
        // Cart items for guest checkout
        cart_items: cartItems
      };

      const response = await fetch('http://localhost:8000/api/payments/initiate/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(paymentData),
      });

      const data = await response.json();

      if (data.success) {
        // Redirect to Pesapal payment page
        window.location.href = data.payment_url;
      } else {
        alert(data.error || 'Payment initiation failed');
      }
    } catch (error) {
      console.error('Error initiating payment:', error);
      alert('Payment initiation failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const totalWithDelivery = (checkoutSummary?.total_ksh || state.total) + deliveryCost;

  return (
    <div className="min-h-screen bg-forest-grey-50">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <Link to="/cart" className="inline-flex items-center text-forest-green-600 hover:text-forest-green-700">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Cart
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Payment Form */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center text-forest-grey-800">
                  <Lock className="w-5 h-5 mr-2" />
                  Secure Payment
                </CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Delivery Information */}
                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <MapPin className="w-5 h-5 text-forest-green-600" />
                      <Label className="text-base font-semibold">Delivery Information</Label>
                    </div>

                    <div>
                      <Label htmlFor="deliveryAddress">Delivery Address</Label>
                      <Textarea
                        id="deliveryAddress"
                        value={deliveryInfo.address}
                        onChange={(e) => handleDeliveryInfoChange('address', e.target.value)}
                        placeholder="Enter your full delivery address"
                        className="mt-1"
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="deliveryCounty">County</Label>
                      <Select value={deliveryInfo.county} onValueChange={(value) => handleDeliveryInfoChange('county', value)}>
                        <SelectTrigger className="mt-1">
                          <SelectValue placeholder="Select your county" />
                        </SelectTrigger>
                        <SelectContent>
                          {KENYAN_COUNTIES.map((county) => (
                            <SelectItem key={county} value={county.toLowerCase()}>
                              {county}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="phone">Phone Number</Label>
                      <Input
                        id="phone"
                        type="tel"
                        value={deliveryInfo.phone}
                        onChange={(e) => handleDeliveryInfoChange('phone', e.target.value)}
                        placeholder="+254 700 000 000"
                        className="mt-1"
                        required
                      />
                    </div>
                  </div>

                  <Separator />

                  {/* Payment Method Selection */}
                  <div>
                    <Label className="text-base font-semibold">Payment Method</Label>
                    <RadioGroup value={paymentMethod} onValueChange={setPaymentMethod} className="mt-3">
                      <div className="flex items-center space-x-2 p-3 border rounded-lg">
                        <RadioGroupItem value="mpesa" id="mpesa" />
                        <Smartphone className="w-5 h-5 text-forest-grey-600" />
                        <Label htmlFor="mpesa" className="flex-1 cursor-pointer">M-Pesa</Label>
                      </div>
                      <div className="flex items-center space-x-2 p-3 border rounded-lg">
                        <RadioGroupItem value="visa" id="visa" />
                        <CreditCard className="w-5 h-5 text-forest-grey-600" />
                        <Label htmlFor="visa" className="flex-1 cursor-pointer">Visa Card</Label>
                      </div>
                      <div className="flex items-center space-x-2 p-3 border rounded-lg">
                        <RadioGroupItem value="mastercard" id="mastercard" />
                        <CreditCard className="w-5 h-5 text-forest-grey-600" />
                        <Label htmlFor="mastercard" className="flex-1 cursor-pointer">Mastercard</Label>
                      </div>
                    </RadioGroup>
                  </div>

                  {/* Payment Information */}
                  <div className="bg-forest-green-50 p-4 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <User className="w-5 h-5 text-forest-green-600" />
                      <span className="font-medium text-forest-green-700">Payment via Pesapal</span>
                    </div>
                    <p className="text-sm text-forest-green-600 mt-2">
                      You will be redirected to Pesapal's secure payment gateway to complete your payment using your selected method.
                    </p>
                  </div>

                  <Separator />

                  {/* Security Notice */}
                  <div className="bg-forest-grey-50 p-4 rounded-lg">
                    <div className="flex items-center space-x-2 text-sm text-forest-grey-600">
                      <Lock className="w-4 h-4" />
                      <span>Your payment information is encrypted and secure</span>
                    </div>
                  </div>

                  <Button
                    type="submit"
                    className="w-full bg-forest-green-600 hover:bg-forest-green-700 text-white"
                    disabled={isLoading || !deliveryInfo.address || !deliveryInfo.county}
                  >
                    {isLoading ? 'Processing...' : `Pay KSH ${totalWithDelivery.toLocaleString()}`}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="text-forest-grey-800">Order Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {state.items.map((item) => (
                  <div key={item.id} className="flex justify-between items-center">
                    <div className="flex-1">
                      <p className="font-medium text-forest-grey-800">{item.name}</p>
                      <p className="text-sm text-forest-grey-600">Qty: {item.quantity}</p>
                    </div>
                    <p className="font-medium text-forest-green-600">
                      <CurrencyFormatter amount={item.price * item.quantity} />
                    </p>
                  </div>
                ))}

                <Separator />

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-forest-grey-600">Subtotal</span>
                    <span className="font-medium">
                      <CurrencyFormatter amount={state.total} />
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-forest-grey-600">Delivery</span>
                    <span className="font-medium">
                      <CurrencyFormatter amount={deliveryCost} />
                    </span>
                  </div>
                  <Separator />
                  <div className="flex justify-between text-lg font-bold">
                    <span className="text-forest-grey-800">Total</span>
                    <span className="text-forest-green-600">
                      <CurrencyFormatter amount={totalWithDelivery} />
                    </span>
                  </div>
                </div>

                <div className="bg-forest-green-50 p-3 rounded-lg">
                  <p className="text-sm text-forest-green-700">
                    <strong>Free delivery</strong> on orders over KSH 10,000 within Nairobi
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutPayment;
