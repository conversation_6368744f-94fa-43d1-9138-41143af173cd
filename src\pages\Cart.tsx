
import React, { useState } from 'react';
import { ShoppingCart, CreditCard, X, ArrowRight, Phone, Plus, Minus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Toggle } from '@/components/ui/toggle';
import { Link, useNavigate } from 'react-router-dom';
import CurrencyFormatter from '@/components/ui/currency-formatter';
import Header from '@/components/Header';
import { useCart } from '@/contexts/CartContext';

const Cart = () => {
  const [paymentMethod, setPaymentMethod] = useState<'mpesa' | 'card'>('mpesa');
  const [phoneNumber, setPhoneNumber] = useState('');
  const { state, updateQuantity, removeItem } = useCart();
  const navigate = useNavigate();

  const deliveryFee = state.total > 10000 ? 0 : 500;
  const total = state.total + deliveryFee;

  const handleQuantityChange = (id: string, newQuantity: number) => {
    updateQuantity(id, newQuantity);
  };

  const handleRemoveItem = (id: string) => {
    removeItem(id);
  };

  const handleCheckout = () => {
    // Navigate to the proper checkout flow for all payment methods
    navigate('/checkout/payment');
  };
  
  return (
    <div className="min-h-screen bg-forest-grey-50">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-4xl font-bold text-forest-grey-800 mb-8 flex items-center gap-3">
          <ShoppingCart className="w-10 h-10" /> Your Cart
        </h1>
        
        {state.items.length > 0 ? (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Cart Items */}
            <div className="col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle>Shopping Cart ({state.items.length} items)</CardTitle>
                </CardHeader>
                <CardContent>
                  {state.items.map((item) => (
                    <div key={item.id} className="flex items-center gap-4 py-4">
                      <img
                        src={item.image}
                        alt={item.name}
                        className="w-20 h-20 object-cover rounded-md"
                      />
                      <div className="flex-grow">
                        <h3 className="font-semibold text-forest-grey-800">{item.name}</h3>
                        <p className="text-forest-green-600 font-medium">
                          <CurrencyFormatter amount={item.price} />
                        </p>
                        <div className="flex items-center mt-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-7 w-7 p-0"
                            onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                            disabled={item.quantity <= 1}
                          >
                            <Minus className="w-3 h-3" />
                          </Button>
                          <span className="mx-2 min-w-[2rem] text-center">{item.quantity}</span>
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-7 w-7 p-0"
                            onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                          >
                            <Plus className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-bold text-forest-grey-800">
                          <CurrencyFormatter amount={item.price * item.quantity} />
                        </p>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-red-500 mt-2"
                          onClick={() => handleRemoveItem(item.id)}
                        >
                          <X className="w-4 h-4" />
                          <span className="ml-1">Remove</span>
                        </Button>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>
            
            {/* Order Summary */}
            <div>
              <Card>
                <CardHeader>
                  <CardTitle>Order Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span>Subtotal</span>
                      <span className="font-semibold">
                        <CurrencyFormatter amount={state.total} />
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Delivery Fee</span>
                      <span className="font-semibold">
                        {deliveryFee === 0 ? 'Free' : <CurrencyFormatter amount={deliveryFee} />}
                      </span>
                    </div>
                    <Separator />
                    <div className="flex justify-between text-lg font-bold">
                      <span>Total</span>
                      <span className="text-forest-green-600">
                        <CurrencyFormatter amount={total} />
                      </span>
                    </div>
                    
                    {/* Payment Methods */}
                    <div className="mt-6">
                      <h3 className="font-semibold mb-3">Payment Method</h3>
                      <div className="flex gap-3">
                        <Toggle 
                          pressed={paymentMethod === 'mpesa'} 
                          onPressedChange={() => setPaymentMethod('mpesa')}
                          className="flex-1 border"
                        >
                          <Phone className="w-4 h-4 mr-2" />
                          M-Pesa
                        </Toggle>
                        <Toggle 
                          pressed={paymentMethod === 'card'} 
                          onPressedChange={() => setPaymentMethod('card')}
                          className="flex-1 border"
                        >
                          <CreditCard className="w-4 h-4 mr-2" />
                          Card
                        </Toggle>
                      </div>
                    </div>
                    
                    {/* Payment Details */}
                    <div className="mt-4">
                      {paymentMethod === 'mpesa' ? (
                        <div className="space-y-3">
                          <label className="block">
                            <span className="text-sm font-medium">M-Pesa Phone Number</span>
                            <input 
                              type="tel" 
                              value={phoneNumber}
                              onChange={(e) => setPhoneNumber(e.target.value)}
                              placeholder="e.g., 0712345678" 
                              className="mt-1 block w-full rounded-md border border-forest-grey-300 p-2"
                            />
                          </label>
                          <p className="text-xs text-forest-grey-500">
                            You'll receive a prompt on your phone to complete payment.
                          </p>
                        </div>
                      ) : (
                        <div className="text-sm text-forest-grey-600">
                          You'll be redirected to our secure payment page.
                        </div>
                      )}
                    </div>
                    
                    <Button 
                      onClick={handleCheckout}
                      className="w-full bg-forest-green-600 hover:bg-forest-green-700 text-white"
                    >
                      Checkout
                      <ArrowRight className="ml-2 w-4 h-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        ) : (
          <div className="text-center py-16">
            <ShoppingCart className="w-16 h-16 mx-auto text-forest-grey-400" />
            <h2 className="mt-4 text-2xl font-semibold text-forest-grey-700">Your cart is empty</h2>
            <p className="mt-2 text-forest-grey-500">Looks like you haven't added any products yet.</p>
            <Button className="mt-6" asChild>
              <Link to="/products">Continue Shopping</Link>
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default Cart;
