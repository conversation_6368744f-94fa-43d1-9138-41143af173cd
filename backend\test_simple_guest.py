#!/usr/bin/env python
"""
Simple test for guest checkout without <PERSON>esapal
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'deepforest_backend.settings')
django.setup()

from payments.pesapal_serializers import PaymentInitiationSerializer
from ecommerce.models import Product, Category, DeliveryZone

def test_serializer():
    """Test the PaymentInitiationSerializer directly"""
    print("🧪 Testing PaymentInitiationSerializer...")
    
    # Get a test product
    product = Product.objects.filter(is_active=True).first()
    if not product:
        print("✗ No active products found")
        return False
    
    print(f"✓ Using product: {product.name} (ID: {product.id})")
    
    # Test data
    data = {
        'delivery_address': '123 Test Street, Nairobi',
        'delivery_county': 'Nairobi',
        'payment_method': 'mpesa',
        'phone': '254700000000',
        'first_name': 'Test',
        'last_name': 'Customer',
        'email': '<EMAIL>',
        'cart_items': [
            {
                'product_id': product.id,
                'quantity': 2
            }
        ]
    }
    
    try:
        serializer = PaymentInitiationSerializer(data=data)
        if serializer.is_valid():
            print("✓ Serializer validation passed")
            
            # Test order creation
            order = serializer.create_order_from_cart(None)  # Guest user
            print(f"✓ Order created: {order.order_reference}")
            print(f"  Total: KSH {order.total_with_delivery}")
            print(f"  User: {order.user}")
            print(f"  Items: {order.items.count()}")
            
            return True
        else:
            print(f"✗ Serializer validation failed: {serializer.errors}")
            return False
            
    except Exception as e:
        print(f"✗ Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🚀 Simple Guest Checkout Test")
    print("=" * 50)
    
    success = test_serializer()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Serializer test passed!")
    else:
        print("❌ Serializer test failed.")

if __name__ == '__main__':
    main()
