"""
DRF serializers for Pesapal payment integration
"""
from rest_framework import serializers
from django.contrib.auth.models import User
from decimal import Decimal

from .models import Order, OrderItem, Payment, PaymentLog
from ecommerce.models import Product, Cart, CartItem, DeliveryZone
from .pesapal_utils import validate_amount, validate_email, validate_phone, generate_order_reference


class OrderItemSerializer(serializers.ModelSerializer):
    """Serializer for order items"""
    product_name = serializers.CharField(source='product.name', read_only=True)
    product_sku = serializers.CharField(source='product.sku', read_only=True)
    total_price = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    
    class Meta:
        model = OrderItem
        fields = ['id', 'product', 'product_name', 'product_sku', 'quantity', 'unit_price_ksh', 'total_price']


class OrderSerializer(serializers.ModelSerializer):
    """Serializer for orders"""
    items = OrderItemSerializer(many=True, read_only=True)
    total_with_delivery = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    user_email = serializers.CharField(source='user.email', read_only=True)
    user_full_name = serializers.SerializerMethodField()
    
    class Meta:
        model = Order
        fields = [
            'id', 'order_reference', 'user', 'user_email', 'user_full_name',
            'total_amount_ksh', 'delivery_cost_ksh', 'total_with_delivery',
            'delivery_address', 'delivery_county', 'payment_status', 'payment_method',
            'pesapal_transaction_id', 'status', 'created_at', 'updated_at', 'paid_at', 'items'
        ]
        read_only_fields = ['id', 'order_reference', 'user', 'pesapal_transaction_id', 'created_at', 'updated_at', 'paid_at']
    
    def get_user_full_name(self, obj):
        """Get user's full name"""
        return f"{obj.user.first_name} {obj.user.last_name}".strip() or obj.user.username


class PaymentInitiationSerializer(serializers.Serializer):
    """Serializer for payment initiation request"""
    delivery_address = serializers.CharField(max_length=500)
    delivery_county = serializers.CharField(max_length=50)
    payment_method = serializers.CharField(max_length=20, required=False)
    phone = serializers.CharField(max_length=15, required=False)

    # Guest customer information (optional for authenticated users)
    first_name = serializers.CharField(max_length=50, required=False)
    last_name = serializers.CharField(max_length=50, required=False)
    email = serializers.EmailField(required=False)

    # Guest cart items (required for guest users)
    cart_items = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        help_text="Cart items for guest checkout: [{'product_id': 1, 'quantity': 2}, ...]"
    )
    
    def validate_delivery_county(self, value):
        """Validate delivery county and get delivery cost"""
        delivery_zone = DeliveryZone.objects.filter(county__iexact=value, is_active=True).first()
        if not delivery_zone:
            raise serializers.ValidationError(f"Delivery not available to {value}")
        return delivery_zone.county
    
    def validate_phone(self, value):
        """Validate phone number format"""
        if value:
            return validate_phone(value)
        return value
    
    def create_order_from_cart(self, user):
        """Create order from user's cart or guest cart items"""
        from ecommerce.models import Product

        # Get delivery cost
        delivery_zone = DeliveryZone.objects.filter(
            county__iexact=self.validated_data['delivery_county'],
            is_active=True
        ).first()

        if not delivery_zone:
            raise serializers.ValidationError(f"Delivery not available to {self.validated_data['delivery_county']}")

        if user and user.is_authenticated:
            # Authenticated user - get cart from database
            try:
                cart = Cart.objects.get(user=user)
                cart_items = cart.items.all()

                if not cart_items.exists():
                    raise serializers.ValidationError("Cart is empty")

                # Calculate totals
                total_amount = sum(item.total_price for item in cart_items)

                # Create order
                order = Order.objects.create(
                    user=user,
                    order_reference=generate_order_reference(user.id if user else None),
                    total_amount_ksh=total_amount,
                    delivery_cost_ksh=delivery_zone.delivery_cost_ksh,
                    delivery_address=self.validated_data['delivery_address'],
                    delivery_county=delivery_zone.county,
                    payment_method=self.validated_data.get('payment_method', '')
                )

                # Create order items
                for cart_item in cart_items:
                    OrderItem.objects.create(
                        order=order,
                        product=cart_item.product,
                        quantity=cart_item.quantity,
                        unit_price_ksh=cart_item.product.current_price
                    )

                # Clear cart
                cart_items.delete()

                return order

            except Cart.DoesNotExist:
                raise serializers.ValidationError("Cart not found")
        else:
            # Guest user - use cart_items from request data
            cart_items_data = self.validated_data.get('cart_items', [])

            if not cart_items_data:
                raise serializers.ValidationError("Cart items are required for guest checkout")

            # Validate and calculate totals
            total_amount = 0
            order_items = []

            for item_data in cart_items_data:
                try:
                    product = Product.objects.get(
                        id=item_data['product_id'],
                        is_active=True
                    )
                    quantity = int(item_data['quantity'])

                    if quantity <= 0:
                        raise serializers.ValidationError(f"Invalid quantity for {product.name}")

                    item_total = product.current_price * quantity
                    total_amount += item_total

                    order_items.append({
                        'product': product,
                        'quantity': quantity,
                        'unit_price': product.current_price
                    })

                except Product.DoesNotExist:
                    raise serializers.ValidationError(f"Product with ID {item_data['product_id']} not found")
                except (KeyError, ValueError, TypeError):
                    raise serializers.ValidationError("Invalid cart item format")

            # Create order for guest
            order = Order.objects.create(
                user=None,  # Guest order
                order_reference=generate_order_reference(None),
                total_amount_ksh=total_amount,
                delivery_cost_ksh=delivery_zone.delivery_cost_ksh,
                delivery_address=self.validated_data['delivery_address'],
                delivery_county=delivery_zone.county,
                payment_method=self.validated_data.get('payment_method', '')
            )

            # Create order items
            for item_data in order_items:
                OrderItem.objects.create(
                    order=order,
                    product=item_data['product'],
                    quantity=item_data['quantity'],
                    unit_price_ksh=item_data['unit_price']
                )

            return order


class PaymentStatusSerializer(serializers.ModelSerializer):
    """Serializer for payment status"""
    order_reference = serializers.CharField(source='order.order_reference', read_only=True)
    
    class Meta:
        model = Payment
        fields = [
            'id', 'order_reference', 'pesapal_transaction_id', 'amount_ksh',
            'payment_method', 'status', 'created_at', 'completed_at'
        ]


class PaymentCallbackSerializer(serializers.Serializer):
    """Serializer for Pesapal callback data"""
    pesapal_notification_type = serializers.CharField()
    pesapal_transaction_tracking_id = serializers.CharField()
    pesapal_merchant_reference = serializers.CharField()
    
    def validate(self, data):
        """Validate callback data"""
        if data['pesapal_notification_type'] not in ['CHANGE', 'COMPLETE']:
            raise serializers.ValidationError("Invalid notification type")
        return data


class PaymentLogSerializer(serializers.ModelSerializer):
    """Serializer for payment logs"""
    order_reference = serializers.CharField(source='order.order_reference', read_only=True)
    
    class Meta:
        model = PaymentLog
        fields = [
            'id', 'order_reference', 'action', 'status', 'message',
            'pesapal_transaction_id', 'pesapal_tracking_id', 'created_at'
        ]


class CheckoutSummarySerializer(serializers.Serializer):
    """Serializer for checkout summary"""
    cart_items = serializers.SerializerMethodField()
    subtotal_ksh = serializers.SerializerMethodField()
    delivery_cost_ksh = serializers.SerializerMethodField()
    total_ksh = serializers.SerializerMethodField()
    delivery_county = serializers.CharField()
    
    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
    
    def get_cart_items(self, obj):
        """Get cart items for the user"""
        if not self.user:
            return []
        
        try:
            cart = Cart.objects.get(user=self.user)
            items = []
            for item in cart.items.all():
                items.append({
                    'product_name': item.product.name,
                    'quantity': item.quantity,
                    'unit_price_ksh': item.product.current_price,
                    'total_price_ksh': item.total_price
                })
            return items
        except Cart.DoesNotExist:
            return []
    
    def get_subtotal_ksh(self, obj):
        """Calculate cart subtotal"""
        if not self.user:
            return Decimal('0.00')
        
        try:
            cart = Cart.objects.get(user=self.user)
            return cart.total_price_ksh
        except Cart.DoesNotExist:
            return Decimal('0.00')
    
    def get_delivery_cost_ksh(self, obj):
        """Get delivery cost for county"""
        county = obj.get('delivery_county')
        if not county:
            return Decimal('0.00')
        
        try:
            delivery_zone = DeliveryZone.objects.get(county__iexact=county, is_active=True)
            return delivery_zone.delivery_cost_ksh
        except DeliveryZone.DoesNotExist:
            return Decimal('0.00')
    
    def get_total_ksh(self, obj):
        """Calculate total including delivery"""
        subtotal = self.get_subtotal_ksh(obj)
        delivery = self.get_delivery_cost_ksh(obj)
        return subtotal + delivery
