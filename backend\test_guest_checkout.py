#!/usr/bin/env python
"""
Test script for guest checkout functionality
"""
import os
import sys
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'deepforest_backend.settings')
django.setup()

from ecommerce.models import Product, Category, DeliveryZone

def setup_test_data():
    """Create test data if it doesn't exist"""
    print("Setting up test data...")
    
    # Create a category
    category, created = Category.objects.get_or_create(
        name="Test Category",
        defaults={'description': 'Test category for guest checkout'}
    )
    
    # Get or create a test product
    try:
        product = Product.objects.filter(is_active=True).first()
        if not product:
            product = Product.objects.create(
                name="Test Product",
                description='Test product for guest checkout',
                category=category,
                current_price=1000.00,
                is_active=True
            )
    except Exception as e:
        print(f"Error creating product: {e}")
        # Just use the first available product
        product = Product.objects.filter(is_active=True).first()
    
    # Create delivery zones
    delivery_zones = [
        {'county': 'Nairobi', 'delivery_cost_ksh': 500},
        {'county': 'Mombasa', 'delivery_cost_ksh': 800},
        {'county': 'Kisumu', 'delivery_cost_ksh': 700},
    ]
    
    for zone_data in delivery_zones:
        zone, created = DeliveryZone.objects.get_or_create(
            county=zone_data['county'],
            defaults={
                'delivery_cost_ksh': zone_data['delivery_cost_ksh'],
                'is_active': True
            }
        )
    
    print(f"✓ Test product created: {product.name} (ID: {product.id})")
    print(f"✓ Delivery zones created")
    return product

def test_guest_checkout():
    """Test guest checkout functionality"""
    print("\n🧪 Testing Guest Checkout...")
    
    # Setup test data
    product = setup_test_data()
    
    # Test payment initiation for guest user
    payment_data = {
        'delivery_address': '123 Test Street, Nairobi',
        'delivery_county': 'Nairobi',
        'payment_method': 'mpesa',
        'phone': '254700000000',
        'first_name': 'Test',
        'last_name': 'Customer',
        'email': '<EMAIL>',
        'cart_items': [
            {
                'product_id': product.id,
                'quantity': 2
            }
        ]
    }
    
    try:
        response = requests.post(
            'http://localhost:8000/api/payments/initiate/',
            headers={'Content-Type': 'application/json'},
            json=payment_data,
            timeout=10
        )
        
        print(f"Response Status: {response.status_code}")
        print(f"Response Data: {response.text}")
        
        if response.status_code == 201:
            data = response.json()
            if data.get('success'):
                print("✓ Guest checkout successful!")
                print(f"  Order Reference: {data['order']['order_reference']}")
                print(f"  Payment URL: {data['payment_url']}")
                return True
            else:
                print(f"✗ Payment initiation failed: {data.get('error')}")
        else:
            print(f"✗ HTTP Error: {response.status_code}")
            try:
                error_data = response.json()
                print(f"  Error details: {error_data}")
            except:
                print(f"  Raw response: {response.text}")
        
    except requests.exceptions.ConnectionError:
        print("✗ Connection error - is the Django server running?")
        print("  Run: python manage.py runserver")
    except Exception as e:
        print(f"✗ Error: {str(e)}")
    
    return False

def main():
    """Main test function"""
    print("🚀 Guest Checkout Test")
    print("=" * 50)
    
    success = test_guest_checkout()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 All tests passed! Guest checkout is working.")
    else:
        print("❌ Tests failed. Check the errors above.")

if __name__ == '__main__':
    main()
